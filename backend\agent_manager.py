"""
Agent Manager for integrating Claude Computer Use agent with FastAPI backend

Author: <PERSON><PERSON> (chirag127)
Created: 2025-07-12T06:35:58.158Z
"""

import asyncio
import json
import os
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from functools import partial

from anthropic.types.beta import (
    BetaContentBlockParam,
    BetaTextBlockParam,
    BetaToolResultBlockParam,
)

# Import the existing computer use demo components
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from computer_use_demo.loop import (
    APIProvider,
    sampling_loop,
)
from computer_use_demo.tools import ToolResult

from database import Database, MessageModel
from websocket_manager import WebSocketManager


class AgentManager:
    def __init__(self):
        self.db = Database()
        self.active_sessions: Dict[str, bool] = {}  # Track active processing sessions

        # Default configuration
        self.default_model = "claude-sonnet-4-20250514"
        self.default_provider = APIProvider.ANTHROPIC
        self.default_max_tokens = 4096
        self.default_tool_version = "computer_use_20250124"

    async def process_message(
        self,
        session_id: str,
        user_message: str,
        websocket_manager: WebSocketManager
    ):
        """Process a user message through the computer use agent"""

        if session_id in self.active_sessions and self.active_sessions[session_id]:
            await websocket_manager.send_error(
                session_id,
                "Session is already processing a message. Please wait for completion."
            )
            return

        self.active_sessions[session_id] = True

        try:
            await websocket_manager.send_status_update(
                session_id,
                "processing",
                "Starting agent processing..."
            )

            # Get session and conversation history
            session = await self.db.get_session(session_id)
            if not session:
                await websocket_manager.send_error(session_id, "Session not found")
                return

            # Get conversation history in Anthropic format
            conversation_history = await self.db.get_conversation_history(session_id)

            # Add the new user message to the conversation
            conversation_history.append({
                "role": "user",
                "content": [BetaTextBlockParam(type="text", text=user_message)]
            })

            # Get API key from environment
            api_key = os.getenv("ANTHROPIC_API_KEY")
            if not api_key:
                await websocket_manager.send_error(
                    session_id,
                    "ANTHROPIC_API_KEY environment variable not set"
                )
                return

            # Create callbacks for real-time updates
            output_callback = partial(
                self._handle_agent_output,
                session_id=session_id,
                websocket_manager=websocket_manager
            )

            tool_output_callback = partial(
                self._handle_tool_output,
                session_id=session_id,
                websocket_manager=websocket_manager
            )

            api_response_callback = partial(
                self._handle_api_response,
                session_id=session_id,
                websocket_manager=websocket_manager
            )

            # Run the agent sampling loop
            updated_messages = await sampling_loop(
                model=self.default_model,
                provider=self.default_provider,
                system_prompt_suffix=session.system_prompt_suffix,
                messages=conversation_history,
                output_callback=output_callback,
                tool_output_callback=tool_output_callback,
                api_response_callback=api_response_callback,
                api_key=api_key,
                only_n_most_recent_images=3,
                max_tokens=self.default_max_tokens,
                tool_version=self.default_tool_version,
                thinking_budget=None,
                token_efficient_tools_beta=False,
            )

            # Save the updated conversation to database
            await self._save_conversation_updates(session_id, conversation_history, updated_messages)

            await websocket_manager.send_status_update(
                session_id,
                "completed",
                "Agent processing completed successfully"
            )

        except Exception as e:
            await websocket_manager.send_error(
                session_id,
                f"Error processing message: {str(e)}"
            )
        finally:
            self.active_sessions[session_id] = False

    async def _handle_agent_output(
        self,
        content: BetaContentBlockParam,
        session_id: str,
        websocket_manager: WebSocketManager
    ):
        """Handle agent output and send real-time updates"""
        try:
            if isinstance(content, dict):
                if content.get("type") == "text":
                    await websocket_manager.send_agent_message(
                        session_id,
                        "assistant",
                        content.get("text", "")
                    )
                elif content.get("type") == "thinking":
                    await websocket_manager.send_agent_thinking(
                        session_id,
                        content.get("thinking", "")
                    )
                elif content.get("type") == "tool_use":
                    await websocket_manager.send_tool_call(
                        session_id,
                        content.get("name", ""),
                        content.get("input", {}),
                        content.get("id", "")
                    )
            elif isinstance(content, str):
                await websocket_manager.send_agent_message(
                    session_id,
                    "assistant",
                    content
                )
        except Exception as e:
            print(f"Error handling agent output: {e}")

    async def _handle_tool_output(
        self,
        tool_output: ToolResult,
        tool_id: str,
        session_id: str,
        websocket_manager: WebSocketManager
    ):
        """Handle tool output and send real-time updates"""
        try:
            result_data = {
                "tool_call_id": tool_id,
                "output": tool_output.output if hasattr(tool_output, 'output') else None,
                "error": tool_output.error if hasattr(tool_output, 'error') else None,
                "base64_image": tool_output.base64_image if hasattr(tool_output, 'base64_image') else None,
            }

            await websocket_manager.send_tool_result(session_id, tool_id, result_data)
        except Exception as e:
            print(f"Error handling tool output: {e}")

    async def _handle_api_response(
        self,
        request,
        response,
        error,
        session_id: str,
        websocket_manager: WebSocketManager
    ):
        """Handle API response for logging/debugging"""
        try:
            if error:
                await websocket_manager.send_error(
                    session_id,
                    f"API Error: {str(error)}",
                    "api_error"
                )
        except Exception as e:
            print(f"Error handling API response: {e}")

    async def _save_conversation_updates(
        self,
        session_id: str,
        original_messages: List[dict],
        updated_messages: List[dict]
    ):
        """Save new messages from the conversation to the database"""
        try:
            # Find new messages that were added during the sampling loop
            original_count = len(original_messages)

            for i, message in enumerate(updated_messages[original_count:], start=original_count):
                message_id = str(uuid.uuid4())

                # Extract content, tool calls, and tool results
                content = ""
                tool_calls = None
                tool_results = None

                if isinstance(message.get("content"), list):
                    text_parts = []
                    tool_call_parts = []
                    tool_result_parts = []

                    for block in message["content"]:
                        if isinstance(block, dict):
                            if block.get("type") == "text":
                                text_parts.append(block.get("text", ""))
                            elif block.get("type") == "tool_use":
                                tool_call_parts.append(block)
                            elif block.get("type") == "tool_result":
                                tool_result_parts.append(block)

                    content = "\n".join(text_parts)
                    if tool_call_parts:
                        tool_calls = json.dumps(tool_call_parts)
                    if tool_result_parts:
                        tool_results = json.dumps(tool_result_parts)

                elif isinstance(message.get("content"), str):
                    content = message["content"]

                # Create and save the message
                db_message = MessageModel(
                    id=message_id,
                    session_id=session_id,
                    role=message["role"],
                    content=content,
                    tool_calls=tool_calls,
                    tool_results=tool_results,
                    created_at=datetime.utcnow()
                )

                await self.db.create_message(db_message)

        except Exception as e:
            print(f"Error saving conversation updates: {e}")

    def is_session_active(self, session_id: str) -> bool:
        """Check if a session is currently processing"""
        return self.active_sessions.get(session_id, False)

    async def stop_session(self, session_id: str, websocket_manager: WebSocketManager):
        """Stop processing for a session"""
        if session_id in self.active_sessions:
            self.active_sessions[session_id] = False
            await websocket_manager.send_status_update(
                session_id,
                "stopped",
                "Session processing stopped by user"
            )
