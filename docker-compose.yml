# Docker Compose for <PERSON> Use Backend
# Author: <PERSON><PERSON> (chirag127)
# Created: 2025-07-12T06:35:58.158Z

version: '3.8'

services:
  # FastAPI Backend Service
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    ports:
      - "8000:8000"
    environment:
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - DATABASE_URL=sqlite:///app/data/claude_computer_use.db
      - DISPLAY=:1
    volumes:
      - ./data:/app/data
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
    depends_on:
      - desktop
    networks:
      - claude-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Desktop Environment with VNC
  desktop:
    build:
      context: .
      dockerfile: Dockerfile.desktop
    ports:
      - "6080:6080"  # noVNC web interface
      - "5900:5900"  # VNC server
    environment:
      - DISPLAY=:1
      - VNC_PASSWORD=password
      - RESOLUTION=1280x720
    volumes:
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
      - ./desktop-data:/home/<USER>
    networks:
      - claude-network
    restart: unless-stopped
    shm_size: 2gb
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6080/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Static File Server
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - claude-network
    restart: unless-stopped

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - backend
      - frontend
      - desktop
    networks:
      - claude-network
    restart: unless-stopped

networks:
  claude-network:
    driver: bridge

volumes:
  data:
    driver: local
  desktop-data:
    driver: local
