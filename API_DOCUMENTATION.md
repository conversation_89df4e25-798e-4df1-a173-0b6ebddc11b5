# API Documentation

**Author: <PERSON><PERSON> (chirag127)**  
**Created: 2025-07-12T06:35:58.158Z**

Complete API documentation for the Claude Computer Use Backend.

## Base URL

```
http://localhost:8000
```

## Authentication

Currently, the API uses Anthropic API key authentication via environment variables. No additional authentication is required for API endpoints.

## Content Types

All API endpoints accept and return JSON unless otherwise specified.

```
Content-Type: application/json
```

## Error Handling

The API returns standard HTTP status codes and JSON error responses:

```json
{
  "detail": "Error message description"
}
```

### Common Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

## Endpoints

### Health Check

#### GET /

Health check endpoint to verify API status.

**Response:**
```json
{
  "message": "Claude Computer Use Backend",
  "version": "1.0.0",
  "status": "running",
  "timestamp": "2025-07-12T06:35:58.158Z"
}
```

### Sessions

#### POST /api/sessions

Create a new chat session.

**Request Body:**
```json
{
  "name": "Session Name (optional)",
  "system_prompt_suffix": "Additional system prompt (optional)"
}
```

**Response:**
```json
{
  "id": "uuid-string",
  "name": "Session Name",
  "created_at": "2025-07-12T06:35:58.158Z",
  "updated_at": "2025-07-12T06:35:58.158Z",
  "status": "active"
}
```

#### GET /api/sessions

List all chat sessions with pagination.

**Query Parameters:**
- `limit` (optional): Number of sessions to return (default: 50)
- `offset` (optional): Number of sessions to skip (default: 0)

**Response:**
```json
{
  "sessions": [
    {
      "id": "uuid-string",
      "name": "Session Name",
      "created_at": "2025-07-12T06:35:58.158Z",
      "updated_at": "2025-07-12T06:35:58.158Z",
      "status": "active"
    }
  ],
  "total": 1
}
```

#### GET /api/sessions/{session_id}

Get details of a specific session.

**Response:**
```json
{
  "id": "uuid-string",
  "name": "Session Name",
  "created_at": "2025-07-12T06:35:58.158Z",
  "updated_at": "2025-07-12T06:35:58.158Z",
  "status": "active"
}
```

#### DELETE /api/sessions/{session_id}

Delete a session and all its messages.

**Response:**
```json
{
  "message": "Session deleted successfully"
}
```

### Messages

#### GET /api/sessions/{session_id}/messages

Get messages for a specific session.

**Query Parameters:**
- `limit` (optional): Number of messages to return (default: 100)
- `offset` (optional): Number of messages to skip (default: 0)

**Response:**
```json
[
  {
    "id": "uuid-string",
    "session_id": "uuid-string",
    "role": "user",
    "content": "Message content",
    "created_at": "2025-07-12T06:35:58.158Z",
    "tool_calls": null,
    "tool_results": null
  },
  {
    "id": "uuid-string",
    "session_id": "uuid-string",
    "role": "assistant",
    "content": "Response content",
    "created_at": "2025-07-12T06:35:58.158Z",
    "tool_calls": [
      {
        "type": "tool_use",
        "id": "tool-call-id",
        "name": "computer",
        "input": {"action": "screenshot"}
      }
    ],
    "tool_results": [
      {
        "type": "tool_result",
        "tool_use_id": "tool-call-id",
        "content": "Tool execution result"
      }
    ]
  }
]
```

#### POST /api/sessions/{session_id}/messages

Send a message to the agent and start processing.

**Request Body:**
```json
{
  "content": "Your message to the agent"
}
```

**Response:**
```json
{
  "message": "Message sent, processing started"
}
```

### VNC Information

#### GET /api/vnc

Get VNC connection information.

**Response:**
```json
{
  "vnc_url": "http://localhost:6080/vnc.html",
  "vnc_port": 6080,
  "display": ":1"
}
```

## WebSocket API

### Connection

Connect to a session for real-time updates:

```
ws://localhost:8000/ws/{session_id}
```

### Message Types

The WebSocket connection sends various message types:

#### Connection Established
```json
{
  "type": "connection_established",
  "session_id": "uuid-string",
  "message": "Connected to session"
}
```

#### Agent Message
```json
{
  "type": "agent_message",
  "role": "assistant",
  "content": "Agent response text",
  "message_id": "uuid-string",
  "timestamp": "2025-07-12T06:35:58.158Z"
}
```

#### Tool Call
```json
{
  "type": "tool_call",
  "tool_name": "computer",
  "tool_input": {
    "action": "screenshot"
  },
  "tool_call_id": "tool-call-id",
  "timestamp": "2025-07-12T06:35:58.158Z"
}
```

#### Tool Result
```json
{
  "type": "tool_result",
  "tool_call_id": "tool-call-id",
  "result": {
    "output": "Tool execution output",
    "error": null,
    "base64_image": "base64-encoded-screenshot"
  },
  "timestamp": "2025-07-12T06:35:58.158Z"
}
```

#### Agent Thinking
```json
{
  "type": "agent_thinking",
  "content": "Agent's internal reasoning",
  "timestamp": "2025-07-12T06:35:58.158Z"
}
```

#### Status Update
```json
{
  "type": "status_update",
  "status": "processing",
  "details": "Agent is processing your request",
  "timestamp": "2025-07-12T06:35:58.158Z"
}
```

#### Error
```json
{
  "type": "error",
  "error_type": "general",
  "message": "Error description",
  "timestamp": "2025-07-12T06:35:58.158Z"
}
```

#### Session Complete
```json
{
  "type": "session_complete",
  "summary": "Task completed successfully",
  "timestamp": "2025-07-12T06:35:58.158Z"
}
```

## Example Usage

### JavaScript Client Example

```javascript
class ClaudeComputerUseClient {
  constructor(baseUrl = 'http://localhost:8000') {
    this.baseUrl = baseUrl;
    this.websocket = null;
  }

  async createSession(name = '', systemPromptSuffix = '') {
    const response = await fetch(`${this.baseUrl}/api/sessions`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: name,
        system_prompt_suffix: systemPromptSuffix
      })
    });
    return await response.json();
  }

  async getSessions(limit = 50, offset = 0) {
    const response = await fetch(
      `${this.baseUrl}/api/sessions?limit=${limit}&offset=${offset}`
    );
    return await response.json();
  }

  async getMessages(sessionId, limit = 100, offset = 0) {
    const response = await fetch(
      `${this.baseUrl}/api/sessions/${sessionId}/messages?limit=${limit}&offset=${offset}`
    );
    return await response.json();
  }

  async sendMessage(sessionId, content) {
    const response = await fetch(
      `${this.baseUrl}/api/sessions/${sessionId}/messages`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ content: content })
      }
    );
    return await response.json();
  }

  connectWebSocket(sessionId, onMessage) {
    const wsUrl = `ws://localhost:8000/ws/${sessionId}`;
    this.websocket = new WebSocket(wsUrl);
    
    this.websocket.onmessage = (event) => {
      const data = JSON.parse(event.data);
      onMessage(data);
    };
    
    this.websocket.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
    
    return this.websocket;
  }

  disconnectWebSocket() {
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }
  }
}

// Usage example
const client = new ClaudeComputerUseClient();

// Create a session
const session = await client.createSession('Weather Search');

// Connect to WebSocket for real-time updates
client.connectWebSocket(session.id, (message) => {
  console.log('Received:', message);
});

// Send a message
await client.sendMessage(session.id, 'Search the weather in Dubai');
```

### Python Client Example

```python
import asyncio
import aiohttp
import websockets
import json

class ClaudeComputerUseClient:
    def __init__(self, base_url='http://localhost:8000'):
        self.base_url = base_url
        
    async def create_session(self, name='', system_prompt_suffix=''):
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f'{self.base_url}/api/sessions',
                json={'name': name, 'system_prompt_suffix': system_prompt_suffix}
            ) as response:
                return await response.json()
    
    async def send_message(self, session_id, content):
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f'{self.base_url}/api/sessions/{session_id}/messages',
                json={'content': content}
            ) as response:
                return await response.json()
    
    async def listen_websocket(self, session_id, message_handler):
        uri = f'ws://localhost:8000/ws/{session_id}'
        async with websockets.connect(uri) as websocket:
            async for message in websocket:
                data = json.loads(message)
                await message_handler(data)

# Usage example
async def main():
    client = ClaudeComputerUseClient()
    
    # Create session
    session = await client.create_session('Weather Search')
    
    # Define message handler
    async def handle_message(data):
        print(f"Received: {data}")
    
    # Start WebSocket listener
    websocket_task = asyncio.create_task(
        client.listen_websocket(session['id'], handle_message)
    )
    
    # Send message
    await client.send_message(session['id'], 'Search the weather in Dubai')
    
    # Wait for WebSocket
    await websocket_task

asyncio.run(main())
```

## Rate Limiting

The API implements rate limiting to prevent abuse:

- API endpoints: 10 requests per second per IP
- WebSocket connections: 5 connections per second per IP

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Request limit per window
- `X-RateLimit-Remaining`: Remaining requests in current window
- `X-RateLimit-Reset`: Time when the rate limit resets

## Monitoring

The API provides several monitoring endpoints:

- `GET /health` - Basic health check
- `GET /metrics` - Prometheus metrics (if enabled)
- WebSocket connection counts via admin endpoints

## Development

For development and testing, you can access:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json
