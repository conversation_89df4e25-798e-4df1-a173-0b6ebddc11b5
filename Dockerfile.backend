# Dockerfile for FastAPI Backend
# Author: <PERSON><PERSON> (chirag127)
# Created: 2025-07-12T06:35:58.158Z

FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    gnupg \
    software-properties-common \
    xvfb \
    x11vnc \
    fluxbox \
    firefox-esr \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY backend/requirements.txt /app/requirements.txt
COPY computer_use_demo/requirements.txt /app/computer_use_demo_requirements.txt

RUN pip install --no-cache-dir -r requirements.txt
RUN pip install --no-cache-dir -r computer_use_demo_requirements.txt

# Copy application code
COPY backend/ /app/backend/
COPY computer_use_demo/ /app/computer_use_demo/

# Create data directory
RUN mkdir -p /app/data

# Set environment variables
ENV PYTHONPATH=/app
ENV DISPLAY=:1

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/ || exit 1

# Start command
CMD ["python", "-m", "uvicorn", "backend.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
