"""
Simple test to verify backend components work
"""
import os
import sys

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_imports():
    """Test if all imports work"""
    try:
        print("Testing database import...")
        from database import Database
        print("✅ Database import successful")
        
        print("Testing websocket manager import...")
        from websocket_manager import WebSocketManager
        print("✅ WebSocket manager import successful")
        
        print("Testing computer use demo imports...")
        from computer_use_demo.loop import APIProvider
        print("✅ Computer use demo import successful")
        
        print("Testing agent manager import...")
        from agent_manager import AgentManager
        print("✅ Agent manager import successful")
        
        print("\n🎉 All imports successful!")
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_fastapi():
    """Test FastAPI app creation"""
    try:
        from fastapi import FastAPI
        app = FastAPI()
        print("✅ FastAPI app creation successful")
        return True
    except Exception as e:
        print(f"❌ FastAPI error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing backend components...\n")
    
    imports_ok = test_imports()
    fastapi_ok = test_fastapi()
    
    if imports_ok and fastapi_ok:
        print("\n✅ All tests passed! Backend should work.")
    else:
        print("\n❌ Some tests failed. Check the errors above.")
