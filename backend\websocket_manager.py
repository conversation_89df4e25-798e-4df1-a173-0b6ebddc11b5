"""
WebSocket Manager for real-time communication

Author: <PERSON><PERSON> (chirag127)
Created: 2025-07-12T06:35:58.158Z
"""

import json
from typing import Dict, List, Set
from fastapi import WebSocket


class WebSocketManager:
    def __init__(self):
        # Dictionary mapping session_id to list of WebSocket connections
        self.active_connections: Dict[str, List[WebSocket]] = {}
        # Set of all active WebSocket connections for quick lookup
        self.all_connections: Set[WebSocket] = set()

    async def connect(self, websocket: WebSocket, session_id: str):
        """Accept a WebSocket connection and add it to the session"""
        await websocket.accept()
        
        if session_id not in self.active_connections:
            self.active_connections[session_id] = []
        
        self.active_connections[session_id].append(websocket)
        self.all_connections.add(websocket)
        
        # Send connection confirmation
        await self.send_to_connection(websocket, {
            "type": "connection_established",
            "session_id": session_id,
            "message": "Connected to session"
        })

    def disconnect(self, websocket: WebSocket, session_id: str):
        """Remove a WebSocket connection"""
        if session_id in self.active_connections:
            if websocket in self.active_connections[session_id]:
                self.active_connections[session_id].remove(websocket)
            
            # Clean up empty session lists
            if not self.active_connections[session_id]:
                del self.active_connections[session_id]
        
        if websocket in self.all_connections:
            self.all_connections.remove(websocket)

    async def send_to_connection(self, websocket: WebSocket, data: dict):
        """Send data to a specific WebSocket connection"""
        try:
            await websocket.send_text(json.dumps(data))
        except Exception as e:
            print(f"Error sending to WebSocket: {e}")
            # Remove the connection if it's broken
            if websocket in self.all_connections:
                self.all_connections.remove(websocket)

    async def send_to_session(self, session_id: str, data: dict):
        """Send data to all WebSocket connections for a session"""
        if session_id not in self.active_connections:
            return
        
        # Create a copy of the list to avoid modification during iteration
        connections = self.active_connections[session_id].copy()
        
        for websocket in connections:
            try:
                await websocket.send_text(json.dumps(data))
            except Exception as e:
                print(f"Error sending to WebSocket in session {session_id}: {e}")
                # Remove the broken connection
                self.disconnect(websocket, session_id)

    async def broadcast_to_all(self, data: dict):
        """Send data to all active WebSocket connections"""
        # Create a copy of the set to avoid modification during iteration
        connections = self.all_connections.copy()
        
        for websocket in connections:
            try:
                await websocket.send_text(json.dumps(data))
            except Exception as e:
                print(f"Error broadcasting to WebSocket: {e}")
                # Remove the broken connection
                if websocket in self.all_connections:
                    self.all_connections.remove(websocket)

    def get_session_connection_count(self, session_id: str) -> int:
        """Get the number of active connections for a session"""
        return len(self.active_connections.get(session_id, []))

    def get_total_connection_count(self) -> int:
        """Get the total number of active connections"""
        return len(self.all_connections)

    def get_active_sessions(self) -> List[str]:
        """Get list of session IDs with active connections"""
        return list(self.active_connections.keys())

    async def send_agent_message(self, session_id: str, role: str, content: str, message_id: str = None):
        """Send an agent message update to the session"""
        await self.send_to_session(session_id, {
            "type": "agent_message",
            "role": role,
            "content": content,
            "message_id": message_id,
            "timestamp": json.dumps({"$date": {"$numberLong": str(int(__import__('time').time() * 1000))}})
        })

    async def send_tool_call(self, session_id: str, tool_name: str, tool_input: dict, tool_call_id: str):
        """Send a tool call update to the session"""
        await self.send_to_session(session_id, {
            "type": "tool_call",
            "tool_name": tool_name,
            "tool_input": tool_input,
            "tool_call_id": tool_call_id,
            "timestamp": json.dumps({"$date": {"$numberLong": str(int(__import__('time').time() * 1000))}})
        })

    async def send_tool_result(self, session_id: str, tool_call_id: str, result: dict):
        """Send a tool result update to the session"""
        await self.send_to_session(session_id, {
            "type": "tool_result",
            "tool_call_id": tool_call_id,
            "result": result,
            "timestamp": json.dumps({"$date": {"$numberLong": str(int(__import__('time').time() * 1000))}})
        })

    async def send_agent_thinking(self, session_id: str, thinking_content: str):
        """Send agent thinking content to the session"""
        await self.send_to_session(session_id, {
            "type": "agent_thinking",
            "content": thinking_content,
            "timestamp": json.dumps({"$date": {"$numberLong": str(int(__import__('time').time() * 1000))}})
        })

    async def send_error(self, session_id: str, error_message: str, error_type: str = "general"):
        """Send an error message to the session"""
        await self.send_to_session(session_id, {
            "type": "error",
            "error_type": error_type,
            "message": error_message,
            "timestamp": json.dumps({"$date": {"$numberLong": str(int(__import__('time').time() * 1000))}})
        })

    async def send_status_update(self, session_id: str, status: str, details: str = None):
        """Send a status update to the session"""
        await self.send_to_session(session_id, {
            "type": "status_update",
            "status": status,
            "details": details,
            "timestamp": json.dumps({"$date": {"$numberLong": str(int(__import__('time').time() * 1000))}})
        })

    async def send_session_complete(self, session_id: str, summary: str = None):
        """Send session completion notification"""
        await self.send_to_session(session_id, {
            "type": "session_complete",
            "summary": summary,
            "timestamp": json.dumps({"$date": {"$numberLong": str(int(__import__('time').time() * 1000))}})
        })
