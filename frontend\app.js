/**
 * Frontend JavaScript for Claude Computer Use Demo
 * Author: <PERSON><PERSON> (chirag127)
 * Created: 2025-07-12T06:35:58.158Z
 */

// Configuration
const API_BASE_URL = "http://localhost:8000/api";
const WS_BASE_URL = "ws://localhost:8000/ws";

// Global state
let currentSessionId = null;
let websocket = null;
let sessions = [];
let isProcessing = false;

// DOM elements
const sessionsList = document.getElementById("sessionsList");
const chatMessages = document.getElementById("chatMessages");
const messageInput = document.getElementById("messageInput");
const sendBtn = document.getElementById("sendBtn");
const chatTitle = document.getElementById("chatTitle");
const chatSubtitle = document.getElementById("chatSubtitle");
const statusIndicator = document.getElementById("statusIndicator");

// Initialize the application
document.addEventListener("DOMContentLoaded", function () {
    loadSessions();
    setupEventListeners();
});

function setupEventListeners() {
    // Auto-resize textarea
    messageInput.addEventListener("input", function () {
        this.style.height = "auto";
        this.style.height = Math.min(this.scrollHeight, 120) + "px";
    });
}

function handleKeyDown(event) {
    if (event.key === "Enter" && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    }
}

// Session management
async function loadSessions() {
    try {
        const response = await fetch(`${API_BASE_URL}/sessions`);
        const data = await response.json();
        sessions = data.sessions;
        renderSessions();
    } catch (error) {
        console.error("Error loading sessions:", error);
        showError("Failed to load sessions");
    }
}

function renderSessions() {
    sessionsList.innerHTML = "";

    sessions.forEach((session) => {
        const sessionElement = document.createElement("div");
        sessionElement.className = `session-item ${
            session.id === currentSessionId ? "active" : ""
        }`;
        sessionElement.onclick = () => selectSession(session.id);

        sessionElement.innerHTML = `
            <div class="session-name">${session.name}</div>
            <div class="session-time">${formatDate(session.created_at)}</div>
        `;

        sessionsList.appendChild(sessionElement);
    });
}

async function createNewSession() {
    try {
        const sessionName = prompt("Enter session name (optional):") || "";

        const response = await fetch(`${API_BASE_URL}/sessions`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                name: sessionName,
                system_prompt_suffix: "",
            }),
        });

        if (!response.ok) {
            throw new Error("Failed to create session");
        }

        const newSession = await response.json();
        sessions.unshift(newSession);
        renderSessions();
        selectSession(newSession.id);
    } catch (error) {
        console.error("Error creating session:", error);
        showError("Failed to create new session");
    }
}

async function selectSession(sessionId) {
    if (currentSessionId === sessionId) return;

    // Close existing WebSocket
    if (websocket) {
        websocket.close();
        websocket = null;
    }

    currentSessionId = sessionId;
    renderSessions();

    // Update UI
    const session = sessions.find((s) => s.id === sessionId);
    if (session) {
        chatTitle.textContent = session.name;
        chatSubtitle.textContent = `Created: ${formatDate(session.created_at)}`;
        messageInput.disabled = false;
        sendBtn.disabled = false;
    }

    // Load messages and connect WebSocket
    await loadMessages(sessionId);
    connectWebSocket(sessionId);
}

async function loadMessages(sessionId) {
    try {
        const response = await fetch(
            `${API_BASE_URL}/sessions/${sessionId}/messages`
        );
        const messages = await response.json();

        chatMessages.innerHTML = "";
        messages.forEach((message) => {
            displayMessage(message.role, message.content, message.created_at);

            // Display tool calls and results if present
            if (message.tool_calls) {
                message.tool_calls.forEach((toolCall) => {
                    displayToolCall(toolCall);
                });
            }

            if (message.tool_results) {
                message.tool_results.forEach((toolResult) => {
                    displayToolResult(toolResult);
                });
            }
        });

        scrollToBottom();
    } catch (error) {
        console.error("Error loading messages:", error);
        showError("Failed to load messages");
    }
}

// WebSocket connection
function connectWebSocket(sessionId) {
    try {
        websocket = new WebSocket(`${WS_BASE_URL}/${sessionId}`);

        websocket.onopen = function (event) {
            console.log("WebSocket connected");
            showStatus("Connected", "completed");
        };

        websocket.onmessage = function (event) {
            const data = JSON.parse(event.data);
            handleWebSocketMessage(data);
        };

        websocket.onclose = function (event) {
            console.log("WebSocket disconnected");
            if (currentSessionId === sessionId) {
                // Try to reconnect after a delay
                setTimeout(() => {
                    if (currentSessionId === sessionId) {
                        connectWebSocket(sessionId);
                    }
                }, 3000);
            }
        };

        websocket.onerror = function (error) {
            console.error("WebSocket error:", error);
            showError("Connection error");
        };
    } catch (error) {
        console.error("Error connecting WebSocket:", error);
        showError("Failed to connect to session");
    }
}

function handleWebSocketMessage(data) {
    switch (data.type) {
        case "agent_message":
            displayMessage("assistant", data.content);
            break;

        case "tool_call":
            displayToolCall({
                name: data.tool_name,
                input: data.tool_input,
                id: data.tool_call_id,
            });
            break;

        case "tool_result":
            displayToolResult(data.result);
            break;

        case "agent_thinking":
            displayThinking(data.content);
            break;

        case "status_update":
            showStatus(data.status, data.status);
            break;

        case "error":
            showError(data.message);
            break;

        case "session_complete":
            showStatus("Task completed", "completed");
            isProcessing = false;
            updateSendButton();
            break;

        default:
            console.log("Unknown message type:", data.type);
    }

    scrollToBottom();
}

// Message sending
async function sendMessage() {
    if (!currentSessionId || isProcessing) return;

    const message = messageInput.value.trim();
    if (!message) return;

    // Display user message immediately
    displayMessage("user", message);
    messageInput.value = "";
    messageInput.style.height = "auto";

    // Update UI state
    isProcessing = true;
    updateSendButton();
    showStatus("Processing...", "processing");

    try {
        const response = await fetch(
            `${API_BASE_URL}/sessions/${currentSessionId}/messages`,
            {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    content: message,
                }),
            }
        );

        if (!response.ok) {
            throw new Error("Failed to send message");
        }
    } catch (error) {
        console.error("Error sending message:", error);
        showError("Failed to send message");
        isProcessing = false;
        updateSendButton();
    }
}

// UI helper functions
function displayMessage(role, content, timestamp = null) {
    const messageElement = document.createElement("div");
    messageElement.className = `message ${role}`;

    const timeStr = timestamp
        ? formatTime(timestamp)
        : formatTime(new Date().toISOString());

    messageElement.innerHTML = `
        <div class="message-header">
            <span class="message-role">${
                role.charAt(0).toUpperCase() + role.slice(1)
            }</span>
            <span class="message-time">${timeStr}</span>
        </div>
        <div class="message-content">${escapeHtml(content)}</div>
    `;

    chatMessages.appendChild(messageElement);
    scrollToBottom();
}

function displayToolCall(toolCall) {
    const messageElement = document.createElement("div");
    messageElement.className = "message tool";

    messageElement.innerHTML = `
        <div class="message-header">
            <span class="message-role">Tool Call</span>
            <span class="message-time">${formatTime(
                new Date().toISOString()
            )}</span>
        </div>
        <div class="message-content">
            <strong>Tool:</strong> ${escapeHtml(toolCall.name)}<br>
            <strong>Input:</strong> <pre>${escapeHtml(
                JSON.stringify(toolCall.input, null, 2)
            )}</pre>
        </div>
    `;

    chatMessages.appendChild(messageElement);
    scrollToBottom();
}

function displayToolResult(result) {
    const messageElement = document.createElement("div");
    messageElement.className = "message tool";

    let resultContent = "";
    if (result.output) {
        resultContent += `<strong>Output:</strong> ${escapeHtml(
            result.output
        )}<br>`;
    }
    if (result.error) {
        resultContent += `<strong>Error:</strong> ${escapeHtml(
            result.error
        )}<br>`;
    }
    if (result.base64_image) {
        resultContent += `<strong>Screenshot:</strong><br><img src="data:image/png;base64,${result.base64_image}" style="max-width: 100%; height: auto;">`;
    }

    messageElement.innerHTML = `
        <div class="message-header">
            <span class="message-role">Tool Result</span>
            <span class="message-time">${formatTime(
                new Date().toISOString()
            )}</span>
        </div>
        <div class="message-content">${resultContent}</div>
    `;

    chatMessages.appendChild(messageElement);
    scrollToBottom();
}

function displayThinking(content) {
    const messageElement = document.createElement("div");
    messageElement.className = "message assistant";

    messageElement.innerHTML = `
        <div class="message-header">
            <span class="message-role">Assistant (Thinking)</span>
            <span class="message-time">${formatTime(
                new Date().toISOString()
            )}</span>
        </div>
        <div class="message-content" style="font-style: italic; opacity: 0.8;">${escapeHtml(
            content
        )}</div>
    `;

    chatMessages.appendChild(messageElement);
    scrollToBottom();
}

function showStatus(message, type) {
    statusIndicator.textContent = message;
    statusIndicator.className = `status-indicator status-${type}`;
    statusIndicator.classList.remove("hidden");

    if (type === "completed" || type === "error") {
        setTimeout(() => {
            statusIndicator.classList.add("hidden");
        }, 3000);
    }
}

function showError(message) {
    showStatus(message, "error");
    displayMessage("error", message);
}

function updateSendButton() {
    sendBtn.disabled = !currentSessionId || isProcessing;
    sendBtn.innerHTML = isProcessing
        ? '<span class="loading"></span>Processing...'
        : "Send";
}

function scrollToBottom() {
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return (
        date.toLocaleDateString() +
        " " +
        date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
    );
}

function formatTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
    });
}

function escapeHtml(text) {
    const div = document.createElement("div");
    div.textContent = text;
    return div.innerHTML;
}
