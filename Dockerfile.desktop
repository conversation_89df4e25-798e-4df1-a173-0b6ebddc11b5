# Dockerfile for Desktop Environment with VNC
# Author: <PERSON><PERSON> (chirag127)
# Created: 2025-07-12T06:35:58.158Z
# Based on the original Claude Computer Use demo

FROM ubuntu:22.04

# Avoid prompts from apt
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    gnupg \
    software-properties-common \
    xvfb \
    x11vnc \
    novnc \
    websockify \
    fluxbox \
    firefox \
    xterm \
    nano \
    vim \
    git \
    python3 \
    python3-pip \
    && rm -rf /var/lib/apt/lists/*

# Create user
RUN useradd -m -s /bin/bash user && \
    echo 'user:password' | chpasswd

# Set up VNC
RUN mkdir -p /home/<USER>/.vnc && \
    echo 'password' | vncpasswd -f > /home/<USER>/.vnc/passwd && \
    chmod 600 /home/<USER>/.vnc/passwd && \
    chown -R user:user /home/<USER>/.vnc

# Copy startup scripts from original image
COPY image/entrypoint.sh /entrypoint.sh
COPY image/start_all.sh /start_all.sh
COPY image/xvfb_startup.sh /xvfb_startup.sh
COPY image/x11vnc_startup.sh /x11vnc_startup.sh
COPY image/novnc_startup.sh /novnc_startup.sh
COPY image/mutter_startup.sh /mutter_startup.sh
COPY image/tint2_startup.sh /tint2_startup.sh

# Make scripts executable
RUN chmod +x /entrypoint.sh /start_all.sh /xvfb_startup.sh /x11vnc_startup.sh /novnc_startup.sh /mutter_startup.sh /tint2_startup.sh

# Copy static content for noVNC
COPY image/static_content /usr/share/novnc/
COPY image/index.html /usr/share/novnc/index.html

# Set up environment
ENV DISPLAY=:1
ENV VNC_PASSWORD=password
ENV RESOLUTION=1280x720

# Expose VNC and noVNC ports
EXPOSE 5900 6080

# Switch to user
USER user
WORKDIR /home/<USER>

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:6080/ || exit 1

# Start services
CMD ["/entrypoint.sh"]
