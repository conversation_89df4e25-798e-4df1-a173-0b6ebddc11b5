<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> Computer Use Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            height: 100vh;
            display: flex;
        }

        .sidebar {
            width: 300px;
            background: white;
            border-right: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
        }

        .new-session-btn {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }

        .new-session-btn:hover {
            background: #0056b3;
        }

        .sessions-list {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
        }

        .session-item {
            padding: 12px;
            margin-bottom: 8px;
            background: #f8f9fa;
            border-radius: 6px;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .session-item:hover {
            background: #e9ecef;
        }

        .session-item.active {
            background: #e3f2fd;
            border-color: #007bff;
        }

        .session-name {
            font-weight: 500;
            margin-bottom: 4px;
        }

        .session-time {
            font-size: 12px;
            color: #666;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .content-area {
            flex: 1;
            display: flex;
        }

        .vnc-panel {
            flex: 1;
            background: white;
            border-right: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
        }

        .vnc-header {
            padding: 15px 20px;
            border-bottom: 1px solid #e0e0e0;
            background: #f8f9fa;
        }

        .vnc-container {
            flex: 1;
            position: relative;
        }

        .vnc-iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        .chat-panel {
            width: 400px;
            background: white;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            padding: 15px 20px;
            border-bottom: 1px solid #e0e0e0;
            background: #f8f9fa;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }

        .message {
            margin-bottom: 16px;
        }

        .message-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .message-role {
            font-weight: 600;
            margin-right: 8px;
        }

        .message-time {
            font-size: 12px;
            color: #666;
        }

        .message-content {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 8px;
            white-space: pre-wrap;
        }

        .message.user .message-content {
            background: #007bff;
            color: white;
        }

        .message.assistant .message-content {
            background: #e8f5e8;
        }

        .message.tool .message-content {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
        }

        .message.error .message-content {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .chat-input {
            padding: 20px;
            border-top: 1px solid #e0e0e0;
        }

        .input-container {
            display: flex;
            gap: 10px;
        }

        .message-input {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            resize: none;
            min-height: 40px;
            max-height: 120px;
        }

        .send-btn {
            padding: 12px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
        }

        .send-btn:hover {
            background: #0056b3;
        }

        .send-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .status-indicator {
            padding: 8px 12px;
            margin: 10px 20px;
            border-radius: 4px;
            font-size: 14px;
            text-align: center;
        }

        .status-processing {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .status-completed {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .status-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .loading {
            display: inline-block;
            width: 12px;
            height: 12px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <button class="new-session-btn" onclick="createNewSession()">
                + New Agent Task
            </button>
        </div>
        <div class="sessions-list" id="sessionsList">
            <!-- Sessions will be populated here -->
        </div>
    </div>

    <div class="main-content">
        <div class="content-area">
            <div class="vnc-panel">
                <div class="vnc-header">
                    <h3>VNC Desktop</h3>
                    <p>Agent computer screen - you can see what the agent is doing</p>
                </div>
                <div class="vnc-container">
                    <iframe 
                        class="vnc-iframe" 
                        src="http://localhost:6080/vnc.html?autoconnect=true&resize=scale"
                        title="VNC Desktop">
                    </iframe>
                </div>
            </div>

            <div class="chat-panel">
                <div class="chat-header">
                    <h3 id="chatTitle">Select a session</h3>
                    <p id="chatSubtitle">Choose a session from the sidebar or create a new one</p>
                </div>
                
                <div id="statusIndicator" class="status-indicator hidden"></div>
                
                <div class="chat-messages" id="chatMessages">
                    <div class="message">
                        <div class="message-content">
                            Welcome! Create a new session or select an existing one to start chatting with the Claude Computer Use agent.
                        </div>
                    </div>
                </div>

                <div class="chat-input">
                    <div class="input-container">
                        <textarea 
                            id="messageInput" 
                            class="message-input" 
                            placeholder="Type your message here..."
                            disabled
                            onkeydown="handleKeyDown(event)">
                        </textarea>
                        <button 
                            id="sendBtn" 
                            class="send-btn" 
                            onclick="sendMessage()"
                            disabled>
                            Send
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
