"""
Database models and operations for Claude Computer Use Backend

Author: <PERSON><PERSON> (chirag127)
Created: 2025-07-12T06:35:58.158Z
"""

import json
import sqlite3
from datetime import datetime
from typing import List, Optional
from dataclasses import dataclass

import aiosqlite


@dataclass
class SessionModel:
    id: str
    name: str
    system_prompt_suffix: str
    status: str
    created_at: datetime
    updated_at: datetime


@dataclass
class MessageModel:
    id: str
    session_id: str
    role: str  # 'user', 'assistant', 'tool'
    content: str
    created_at: datetime
    tool_calls: Optional[str] = None  # JSON string
    tool_results: Optional[str] = None  # JSON string


class Database:
    def __init__(self, db_path: str = "claude_computer_use.db"):
        self.db_path = db_path
        self.connection = None

    async def init_db(self):
        """Initialize the database and create tables"""
        self.connection = await aiosqlite.connect(self.db_path)
        
        # Enable foreign key constraints
        await self.connection.execute("PRAGMA foreign_keys = ON")
        
        # Create sessions table
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS sessions (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                system_prompt_suffix TEXT DEFAULT '',
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create messages table
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS messages (
                id TEXT PRIMARY KEY,
                session_id TEXT NOT NULL,
                role TEXT NOT NULL,
                content TEXT NOT NULL,
                tool_calls TEXT,
                tool_results TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES sessions (id) ON DELETE CASCADE
            )
        """)
        
        # Create indexes for better performance
        await self.connection.execute("""
            CREATE INDEX IF NOT EXISTS idx_messages_session_id 
            ON messages (session_id)
        """)
        
        await self.connection.execute("""
            CREATE INDEX IF NOT EXISTS idx_messages_created_at 
            ON messages (created_at)
        """)
        
        await self.connection.execute("""
            CREATE INDEX IF NOT EXISTS idx_sessions_created_at 
            ON sessions (created_at)
        """)
        
        await self.connection.commit()

    async def close(self):
        """Close the database connection"""
        if self.connection:
            await self.connection.close()

    async def create_session(self, session: SessionModel) -> None:
        """Create a new session"""
        await self.connection.execute("""
            INSERT INTO sessions (id, name, system_prompt_suffix, status, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            session.id,
            session.name,
            session.system_prompt_suffix,
            session.status,
            session.created_at.isoformat(),
            session.updated_at.isoformat()
        ))
        await self.connection.commit()

    async def get_session(self, session_id: str) -> Optional[SessionModel]:
        """Get a session by ID"""
        cursor = await self.connection.execute("""
            SELECT id, name, system_prompt_suffix, status, created_at, updated_at
            FROM sessions WHERE id = ?
        """, (session_id,))
        
        row = await cursor.fetchone()
        if not row:
            return None
        
        return SessionModel(
            id=row[0],
            name=row[1],
            system_prompt_suffix=row[2],
            status=row[3],
            created_at=datetime.fromisoformat(row[4]),
            updated_at=datetime.fromisoformat(row[5])
        )

    async def get_sessions(self, limit: int = 50, offset: int = 0) -> List[SessionModel]:
        """Get all sessions with pagination"""
        cursor = await self.connection.execute("""
            SELECT id, name, system_prompt_suffix, status, created_at, updated_at
            FROM sessions 
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        """, (limit, offset))
        
        rows = await cursor.fetchall()
        return [
            SessionModel(
                id=row[0],
                name=row[1],
                system_prompt_suffix=row[2],
                status=row[3],
                created_at=datetime.fromisoformat(row[4]),
                updated_at=datetime.fromisoformat(row[5])
            )
            for row in rows
        ]

    async def get_sessions_count(self) -> int:
        """Get total number of sessions"""
        cursor = await self.connection.execute("SELECT COUNT(*) FROM sessions")
        row = await cursor.fetchone()
        return row[0] if row else 0

    async def update_session(self, session_id: str, **kwargs) -> None:
        """Update session fields"""
        set_clauses = []
        values = []
        
        for key, value in kwargs.items():
            if key in ['name', 'system_prompt_suffix', 'status']:
                set_clauses.append(f"{key} = ?")
                values.append(value)
        
        if set_clauses:
            set_clauses.append("updated_at = ?")
            values.append(datetime.utcnow().isoformat())
            values.append(session_id)
            
            query = f"UPDATE sessions SET {', '.join(set_clauses)} WHERE id = ?"
            await self.connection.execute(query, values)
            await self.connection.commit()

    async def delete_session(self, session_id: str) -> None:
        """Delete a session and all its messages"""
        await self.connection.execute("DELETE FROM sessions WHERE id = ?", (session_id,))
        await self.connection.commit()

    async def create_message(self, message: MessageModel) -> None:
        """Create a new message"""
        await self.connection.execute("""
            INSERT INTO messages (id, session_id, role, content, tool_calls, tool_results, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            message.id,
            message.session_id,
            message.role,
            message.content,
            message.tool_calls,
            message.tool_results,
            message.created_at.isoformat()
        ))
        await self.connection.commit()

    async def get_messages(self, session_id: str, limit: int = 100, offset: int = 0) -> List[MessageModel]:
        """Get messages for a session with pagination"""
        cursor = await self.connection.execute("""
            SELECT id, session_id, role, content, tool_calls, tool_results, created_at
            FROM messages 
            WHERE session_id = ?
            ORDER BY created_at ASC
            LIMIT ? OFFSET ?
        """, (session_id, limit, offset))
        
        rows = await cursor.fetchall()
        return [
            MessageModel(
                id=row[0],
                session_id=row[1],
                role=row[2],
                content=row[3],
                tool_calls=row[4],
                tool_results=row[5],
                created_at=datetime.fromisoformat(row[6])
            )
            for row in rows
        ]

    async def get_conversation_history(self, session_id: str) -> List[dict]:
        """Get conversation history in Anthropic API format"""
        messages = await self.get_messages(session_id)
        
        conversation = []
        for message in messages:
            if message.role in ['user', 'assistant']:
                msg_content = []
                
                # Add text content
                if message.content:
                    msg_content.append({
                        "type": "text",
                        "text": message.content
                    })
                
                # Add tool calls if present
                if message.tool_calls:
                    tool_calls = json.loads(message.tool_calls)
                    msg_content.extend(tool_calls)
                
                # Add tool results if present
                if message.tool_results:
                    tool_results = json.loads(message.tool_results)
                    msg_content.extend(tool_results)
                
                conversation.append({
                    "role": message.role,
                    "content": msg_content if len(msg_content) > 1 else message.content
                })
        
        return conversation
