#!/usr/bin/env python3
"""
Startup script for Claude Computer Use Backend
Author: <PERSON><PERSON> (chirag127)
Created: 2025-07-12T06:35:58.158Z
"""

import os
import sys
import uvicorn

# Add the project root and backend directory to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
backend_dir = os.path.join(project_root, 'backend')
sys.path.insert(0, project_root)
sys.path.insert(0, backend_dir)

# Set default environment variables if not already set
if not os.getenv("ANTHROPIC_API_KEY"):
    print("⚠️  WARNING: ANTHROPIC_API_KEY not set!")
    print("Please set your API key:")
    print("$env:ANTHROPIC_API_KEY='your_api_key_here'")

if not os.getenv("DISPLAY"):
    os.environ["DISPLAY"] = ":1"

def main():
    """Start the FastAPI backend server"""
    print("🚀 Starting Claude Computer Use Backend...")
    print(f"📁 Project root: {project_root}")
    print(f"🔑 API Key set: {'✅' if os.getenv('ANTHROPIC_API_KEY') else '❌'}")
    print(f"🖥️  Display: {os.getenv('DISPLAY', 'Not set')}")
    print()
    print("📡 Server will be available at:")
    print("   • Backend API: http://localhost:8080")
    print("   • API Docs: http://localhost:8080/docs")
    print()
    print("Press CTRL+C to stop the server")
    print("-" * 50)

    try:
        uvicorn.run(
            "backend.main:app",
            host="0.0.0.0",
            port=8080,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
