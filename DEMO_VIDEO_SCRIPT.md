# Demo Video Script

**Author: <PERSON><PERSON> (chirag127)**  
**Created: 2025-07-12T06:35:58.158Z**

## Video Overview

**Duration**: 5 minutes  
**Title**: "Claude Computer Use Backend - Scalable Session Management Demo"

## Script Structure

### 1. Introduction (30 seconds)

**[Screen: Show README-BACKEND.md]**

"Hello! I'm <PERSON><PERSON>, and today I'll demonstrate the Claude Computer Use Backend - a scalable FastAPI replacement for the original Streamlit interface. This system provides session management, real-time streaming, and VNC integration for <PERSON>'s computer use capabilities."

**Key Points to Mention:**
- Replaced experimental Streamlit with production-ready FastAPI
- Added session persistence with SQLite database
- Real-time WebSocket communication
- Complete Docker containerization

### 2. Repository and Codebase Overview (60 seconds)

**[Screen: Show project structure in file explorer]**

"Let me walk you through the codebase architecture:"

**Show and explain:**
- `backend/` - FastAPI application with main.py, database.py, agent_manager.py, websocket_manager.py
- `frontend/` - Simple HTML/JavaScript demo interface
- `computer_use_demo/` - Original Anthropic demo components (reused)
- `docker-compose.yml` - Multi-service deployment configuration
- `Dockerfile.*` - Separate containers for backend, frontend, and desktop

**[Screen: Show docker-compose.yml]**

"The architecture uses three main services: FastAPI backend on port 8000, VNC desktop on port 6080, and frontend on port 3000, all orchestrated with Docker Compose."

### 3. Service Launch and Endpoint Functionality (60 seconds)

**[Screen: Terminal showing docker-compose commands]**

"Let's start the services:"

```bash
# Show environment setup
cat .env.example

# Start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f backend
```

**[Screen: Show browser with API documentation]**

"The FastAPI backend provides comprehensive API documentation at localhost:8000/docs"

**Show key endpoints:**
- POST /api/sessions - Create new session
- GET /api/sessions - List sessions
- POST /api/sessions/{id}/messages - Send messages
- WS /ws/{session_id} - WebSocket for real-time updates

### 4. Usage Case 1: Weather Search in Dubai (90 seconds)

**[Screen: Open frontend at localhost:3000]**

"Now let's demonstrate the first usage case - searching weather in Dubai."

**Steps to demonstrate:**
1. **Open frontend**: "Here's our simple but functional frontend interface"
2. **Show VNC panel**: "On the left, we have the VNC desktop where we can see the agent's actions"
3. **Create new session**: Click "New Agent Task", enter "Dubai Weather Search"
4. **Send message**: Type "Search the weather in Dubai"
5. **Show real-time updates**: Point out the WebSocket messages appearing in real-time
6. **VNC interaction**: Show Firefox opening, Google search, weather results
7. **Final result**: Show the agent's summary response

**Key points to highlight:**
- Real-time streaming of each step
- VNC showing actual browser automation
- WebSocket messages for tool calls and results
- Session persistence in the sidebar

### 5. Usage Case 2: Weather Search in San Francisco (90 seconds)

**[Screen: Continue with same frontend]**

"Let's create a second session to demonstrate session management."

**Steps to demonstrate:**
1. **Create second session**: Click "New Agent Task", enter "SF Weather Search"
2. **Send message**: Type "Search the weather in San Francisco"
3. **Show parallel processing**: Demonstrate that this is a separate session
4. **Monitor progress**: Show real-time updates for the new task
5. **Session switching**: Click between sessions to show history persistence
6. **Verify storage**: Show that both sessions maintain their chat history

**Key points to highlight:**
- Multiple concurrent sessions
- Independent session histories
- Database persistence working correctly
- Session switching functionality

### 6. Streamlit-like UI Behavior Simulation (60 seconds)

**[Screen: Focus on chat panel and status indicators]**

"The system replicates and improves upon the original Streamlit behavior:"

**Demonstrate:**
1. **Real-time progress**: Show status indicators changing (Processing → Completed)
2. **Tool execution**: Point out tool calls appearing in real-time
3. **Agent thinking**: Show thinking process if visible
4. **Error handling**: Demonstrate graceful error handling
5. **Task completion**: Show completion notification and prompt for new task

**[Screen: Show WebSocket developer tools]**

"Behind the scenes, WebSocket messages provide real-time updates for every agent action, tool call, and result."

### 7. Technical Highlights and Architecture Benefits (30 seconds)

**[Screen: Show architecture diagram or code]**

"Key technical achievements:"

- **Scalability**: FastAPI backend can handle multiple concurrent sessions
- **Persistence**: SQLite database stores all session data
- **Real-time**: WebSocket connections provide live updates
- **Modularity**: Separate services for backend, frontend, and desktop
- **Production-ready**: Docker deployment with health checks and monitoring

### 8. Conclusion (30 seconds)

**[Screen: Show README-BACKEND.md or project overview]**

"This Claude Computer Use Backend successfully transforms the experimental Streamlit demo into a production-ready system with:"

- Session management and persistence
- Real-time streaming capabilities
- Scalable FastAPI architecture
- Complete Docker deployment
- Clean API design

"The system is ready for production use and can be easily extended with additional features like user authentication, advanced session management, and monitoring."

**[Screen: Show GitHub repository]**

"Thank you for watching! The complete code, documentation, and setup instructions are available in the repository."

## Recording Tips

### Technical Setup
- **Screen Resolution**: 1920x1080 for clear visibility
- **Browser Zoom**: 125% for better text readability
- **Terminal Font**: Large, clear font (14pt+)
- **Multiple Monitors**: Use one for recording, one for notes

### Recording Checklist

**Before Recording:**
- [ ] All services running and healthy
- [ ] ANTHROPIC_API_KEY configured
- [ ] VNC desktop accessible
- [ ] Frontend loading correctly
- [ ] Clear browser cache and history
- [ ] Close unnecessary applications

**During Recording:**
- [ ] Speak clearly and at moderate pace
- [ ] Pause between major sections
- [ ] Show actual functionality, not just code
- [ ] Highlight real-time updates
- [ ] Demonstrate error handling
- [ ] Keep within 5-minute limit

**After Recording:**
- [ ] Verify audio quality
- [ ] Check all demonstrations worked
- [ ] Ensure key points were covered
- [ ] Add captions if needed

### Key Messages to Emphasize

1. **Production-Ready**: This is not a demo, but a production-ready system
2. **Real-time**: Emphasize the live streaming capabilities
3. **Scalable**: Multiple sessions, Docker deployment
4. **Complete**: Full replacement of Streamlit with better functionality
5. **Easy Setup**: Docker Compose makes deployment simple

### Potential Issues and Solutions

**If VNC doesn't load:**
- Check desktop container status
- Verify port 6080 is accessible
- Restart desktop service

**If WebSocket disconnects:**
- Show reconnection functionality
- Demonstrate error handling

**If agent fails:**
- Show error messages in UI
- Demonstrate graceful degradation

**If services are slow:**
- Mention this is expected in demo environment
- Highlight that production would be faster

## Post-Production

### Video Editing
- Add title slide with project name and author
- Include timestamps for major sections
- Add captions for key technical terms
- Ensure smooth transitions between sections

### Upload Checklist
- [ ] Video quality: 1080p minimum
- [ ] Audio clear and synchronized
- [ ] Duration under 5 minutes
- [ ] Title includes "Claude Computer Use Backend Demo"
- [ ] Description includes GitHub repository link
- [ ] Tags: FastAPI, Claude, Computer Use, Docker, WebSocket
