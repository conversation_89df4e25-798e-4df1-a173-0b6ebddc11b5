# FastAPI Backend Requirements for Claude Computer Use
# Author: <PERSON><PERSON> (chirag127)
# Created: 2025-07-12T06:35:58.158Z

# Core FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Database
aiosqlite==0.19.0

# WebSocket support
websockets==12.0

# HTTP client for API calls
httpx==0.25.2

# Anthropic API client
anthropic==0.34.2

# Data validation and serialization
pydantic==2.5.0

# Environment variable management
python-dotenv==1.0.0

# CORS middleware
python-multipart==0.0.6

# Async support
asyncio-mqtt==0.13.0

# Logging and monitoring
structlog==23.2.0

# Development dependencies
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
flake8==6.1.0

# Computer Use Demo dependencies (from original project)
# These are needed for the agent integration
pillow==10.1.0
typing-extensions==4.8.0
