# Environment Variables for Claude Computer Use Backend
# Author: <PERSON><PERSON> (chirag127)
# Created: 2025-07-12T06:35:58.158Z

# Anthropic API Configuration
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Database Configuration
DATABASE_URL=sqlite:///app/data/claude_computer_use.db

# Display Configuration
DISPLAY=:1

# VNC Configuration
VNC_PASSWORD=password
RESOLUTION=1280x720

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000

# Development/Production Mode
ENVIRONMENT=development

# Logging Level
LOG_LEVEL=INFO

# CORS Configuration (for development)
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]

# Security (change in production)
SECRET_KEY=your-secret-key-here

# Optional: Custom system prompt suffix
SYSTEM_PROMPT_SUFFIX=""

# Optional: Model configuration
DEFAULT_MODEL=claude-sonnet-4-20250514
DEFAULT_PROVIDER=anthropic
DEFAULT_MAX_TOKENS=4096
DEFAULT_TOOL_VERSION=computer_use_20250124
