"""
FastAPI Backend for Claude Computer Use Agent Session Management

Author: <PERSON><PERSON> (chirag127)
Created: 2025-07-12T06:35:58.158Z
"""

import asyncio
import json
import uuid
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Dict, List, Optional

import uvicorn
from fastapi import FastAP<PERSON>, HTTPException, WebSocket, WebSocketDisconnect, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel

from .database import Database, SessionModel, MessageModel
from .agent_manager import AgentManager
from .websocket_manager import WebSocketManager


# Pydantic models for API
class SessionCreate(BaseModel):
    name: Optional[str] = None
    system_prompt_suffix: Optional[str] = ""


class SessionResponse(BaseModel):
    id: str
    name: str
    created_at: datetime
    updated_at: datetime
    status: str


class MessageCreate(BaseModel):
    content: str


class MessageResponse(BaseModel):
    id: str
    session_id: str
    role: str
    content: str
    created_at: datetime
    tool_calls: Optional[List[Dict]] = None
    tool_results: Optional[List[Dict]] = None


class SessionListResponse(BaseModel):
    sessions: List[SessionResponse]
    total: int


# Global managers
db = Database()
agent_manager = AgentManager()
websocket_manager = WebSocketManager()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Initialize and cleanup resources"""
    # Initialize database
    await db.init_db()
    yield
    # Cleanup
    await db.close()


app = FastAPI(
    title="Claude Computer Use Backend",
    description="Scalable backend for computer use agent session management",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# API Routes
@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "Claude Computer Use Backend",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.utcnow().isoformat()
    }


@app.post("/api/sessions", response_model=SessionResponse)
async def create_session(session_data: SessionCreate):
    """Create a new chat session"""
    session_id = str(uuid.uuid4())
    session_name = session_data.name or f"Session {datetime.utcnow().strftime('%Y-%m-%d %H:%M')}"
    
    session = SessionModel(
        id=session_id,
        name=session_name,
        system_prompt_suffix=session_data.system_prompt_suffix or "",
        status="active",
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    
    await db.create_session(session)
    
    return SessionResponse(
        id=session.id,
        name=session.name,
        created_at=session.created_at,
        updated_at=session.updated_at,
        status=session.status
    )


@app.get("/api/sessions", response_model=SessionListResponse)
async def list_sessions(limit: int = 50, offset: int = 0):
    """List all chat sessions"""
    sessions = await db.get_sessions(limit=limit, offset=offset)
    total = await db.get_sessions_count()
    
    session_responses = [
        SessionResponse(
            id=session.id,
            name=session.name,
            created_at=session.created_at,
            updated_at=session.updated_at,
            status=session.status
        )
        for session in sessions
    ]
    
    return SessionListResponse(sessions=session_responses, total=total)


@app.get("/api/sessions/{session_id}", response_model=SessionResponse)
async def get_session(session_id: str):
    """Get a specific session"""
    session = await db.get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    return SessionResponse(
        id=session.id,
        name=session.name,
        created_at=session.created_at,
        updated_at=session.updated_at,
        status=session.status
    )


@app.delete("/api/sessions/{session_id}")
async def delete_session(session_id: str):
    """Delete a session and all its messages"""
    session = await db.get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    await db.delete_session(session_id)
    return {"message": "Session deleted successfully"}


@app.get("/api/sessions/{session_id}/messages", response_model=List[MessageResponse])
async def get_session_messages(session_id: str, limit: int = 100, offset: int = 0):
    """Get messages for a specific session"""
    session = await db.get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    messages = await db.get_messages(session_id, limit=limit, offset=offset)
    
    return [
        MessageResponse(
            id=message.id,
            session_id=message.session_id,
            role=message.role,
            content=message.content,
            created_at=message.created_at,
            tool_calls=json.loads(message.tool_calls) if message.tool_calls else None,
            tool_results=json.loads(message.tool_results) if message.tool_results else None
        )
        for message in messages
    ]


@app.post("/api/sessions/{session_id}/messages")
async def send_message(session_id: str, message_data: MessageCreate):
    """Send a message to the agent and start processing"""
    session = await db.get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    # Create user message
    user_message = MessageModel(
        id=str(uuid.uuid4()),
        session_id=session_id,
        role="user",
        content=message_data.content,
        created_at=datetime.utcnow()
    )
    
    await db.create_message(user_message)
    
    # Start agent processing in background
    asyncio.create_task(
        agent_manager.process_message(session_id, message_data.content, websocket_manager)
    )
    
    return {"message": "Message sent, processing started"}


# WebSocket endpoint for real-time communication
@app.websocket("/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    """WebSocket endpoint for real-time communication with a session"""
    await websocket_manager.connect(websocket, session_id)
    
    try:
        while True:
            # Keep connection alive and handle any incoming messages
            data = await websocket.receive_text()
            # Echo back for now - could be used for interrupts or other commands
            await websocket_manager.send_to_session(session_id, {
                "type": "echo",
                "data": data
            })
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket, session_id)


# VNC proxy endpoint (to be implemented)
@app.get("/api/vnc")
async def vnc_info():
    """Get VNC connection information"""
    return {
        "vnc_url": "http://localhost:6080/vnc.html",
        "vnc_port": 6080,
        "display": ":1"
    }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
